# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# Get current directory
current_dir = Path.cwd()

# Data files to include
data_files = [
    ('demetify_ncomms2022_app.py', '.'),
    ('ncomms2022_model_enhanced.py', '.'),
    ('ncomms2022_preprocessing_fsl.py', '.'),
    ('ncomms2022_shap.py', '.'),
    ('requirements.txt', '.'),
]

# Add model directory
model_dir = current_dir / 'ncomms2022_original'
if model_dir.exists():
    for root, dirs, files in os.walk(model_dir):
        for file in files:
            src_path = Path(root) / file
            rel_path = src_path.relative_to(current_dir)
            dest_dir = str(rel_path.parent)
            data_files.append((str(src_path), dest_dir))

a = Analysis(
    ['simple_launcher.py'],
    pathex=[],
    binaries=[],
    datas=data_files,
    hiddenimports=[
        'streamlit',
        'streamlit.web.cli',
        'streamlit.runtime.scriptrunner.script_runner',
        'torch',
        'numpy',
        'pandas',
        'matplotlib',
        'scikit-learn',
        'nibabel',
        'nilearn',
        'shap',
        'PIL',
        'tkinter'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='demetify',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)