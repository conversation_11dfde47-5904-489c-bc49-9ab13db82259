@echo off

REM Demetify - One-Click Installer for Windows
REM Installs Python, Conda, and all dependencies automatically

echo 🧠 Demetify - One-Click Installer
echo =================================
echo Installing Python environment and dependencies...
echo.

REM Check if conda is available
where conda >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo [SUCCESS] Conda already available
    goto setup_environment
)

REM Check common installation paths
if exist "%USERPROFILE%\miniconda3\Scripts\conda.exe" (
    echo [SUCCESS] Found Miniconda in user directory
    set "CONDA_PATH=%USERPROFILE%\miniconda3"
    goto setup_environment
)

echo [INFO] Installing Miniconda...
set "TEMP_DIR=%TEMP%\demetify_install"
if not exist "%TEMP_DIR%" mkdir "%TEMP_DIR%"
cd /d "%TEMP_DIR%"

REM Download Miniconda installer
powershell -Command "Invoke-WebRequest -Uri 'https://repo.anaconda.com/miniconda/Miniconda3-latest-Windows-x86_64.exe' -OutFile 'Miniconda3-latest-Windows-x86_64.exe'"

REM Install Miniconda silently
"Miniconda3-latest-Windows-x86_64.exe" /InstallationType=JustMe /RegisterPython=1 /S /D=%USERPROFILE%\miniconda3

REM Wait for installation
timeout /t 30 /nobreak >nul

REM Clean up
cd /d "%~dp0"
rmdir /s /q "%TEMP_DIR%"

set "CONDA_PATH=%USERPROFILE%\miniconda3"
echo [SUCCESS] Miniconda installed

:setup_environment
echo [INFO] Setting up demetify environment...

REM Initialize conda
if defined CONDA_PATH (
    call "%CONDA_PATH%\Scripts\activate.bat"
) else (
    call conda activate base
)

REM Create environment
conda create -n demetify python=3.9 -y

REM Activate environment
call conda activate demetify

REM Install dependencies
echo [INFO] Installing dependencies...
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y
if %ERRORLEVEL% NEQ 0 (
    conda install pytorch torchvision torchaudio cpuonly -c pytorch -y
)

pip install -r requirements.txt

REM Create launcher
echo [INFO] Creating launcher script...
echo @echo off > launch_demetify.bat
echo echo 🧠 Starting Demetify... >> launch_demetify.bat
echo. >> launch_demetify.bat
echo REM Activate environment >> launch_demetify.bat
echo if exist "%%USERPROFILE%%\miniconda3\Scripts\activate.bat" ^( >> launch_demetify.bat
echo     call "%%USERPROFILE%%\miniconda3\Scripts\activate.bat" demetify >> launch_demetify.bat
echo ^) else ^( >> launch_demetify.bat
echo     call conda activate demetify >> launch_demetify.bat
echo ^) >> launch_demetify.bat
echo. >> launch_demetify.bat
echo echo 🚀 Launching Demetify at http://localhost:8501 >> launch_demetify.bat
echo streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address 0.0.0.0 >> launch_demetify.bat
echo pause >> launch_demetify.bat

REM Test system
echo [INFO] Testing installation...
python test_system.py

echo.
echo [SUCCESS] 🎉 Installation complete!
echo.
echo To launch Demetify:
echo   Double-click: launch_demetify.bat
echo.
echo Then open: http://localhost:8501
echo.

pause
