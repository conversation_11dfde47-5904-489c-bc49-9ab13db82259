#!/bin/bash

# Demetify - One-Click Installer for Linux/Mac
# Installs Python, Conda, and all dependencies automatically

set -e

echo "🧠 Demetify - One-Click Installer"
echo "================================="
echo "Installing Python environment and dependencies..."
echo ""

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Check if conda is available
if ! command -v conda &> /dev/null; then
    print_status "Installing Miniconda..."
    
    # Create temp directory
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"
    
    # Download and install Miniconda
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        curl -L -O "https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh"
        bash "Miniconda3-latest-Linux-x86_64.sh" -b -p "$HOME/miniconda3"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        curl -L -O "https://repo.anaconda.com/miniconda/Miniconda3-latest-MacOSX-x86_64.sh"
        bash "Miniconda3-latest-MacOSX-x86_64.sh" -b -p "$HOME/miniconda3"
    fi
    
    # Initialize conda
    "$HOME/miniconda3/bin/conda" init bash
    source "$HOME/miniconda3/etc/profile.d/conda.sh"
    
    # Clean up
    cd - > /dev/null
    rm -rf "$TEMP_DIR"
    
    print_success "Miniconda installed"
else
    print_success "Conda already available"
fi

# Source conda
if [[ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]]; then
    source "$HOME/miniconda3/etc/profile.d/conda.sh"
elif command -v conda &> /dev/null; then
    eval "$(conda shell.bash hook)"
fi

# Create environment
print_status "Creating demetify environment..."
conda create -n demetify python=3.9 -y

# Activate environment
conda activate demetify

# Install dependencies
print_status "Installing dependencies..."
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y || \
conda install pytorch torchvision torchaudio cpuonly -c pytorch -y

pip install -r requirements.txt

# Create launcher
print_status "Creating launcher script..."
cat > launch_demetify.sh << 'EOF'
#!/bin/bash
echo "🧠 Starting Demetify..."

# Source conda
if [[ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]]; then
    source "$HOME/miniconda3/etc/profile.d/conda.sh"
elif command -v conda &> /dev/null; then
    eval "$(conda shell.bash hook)"
fi

# Activate environment
conda activate demetify

echo "🚀 Launching Demetify at http://localhost:8501"
streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address 0.0.0.0
EOF

chmod +x launch_demetify.sh

# Test system
print_status "Testing installation..."
python test_system.py

echo ""
print_success "🎉 Installation complete!"
echo ""
echo "To launch Demetify:"
echo "  ./launch_demetify.sh"
echo ""
echo "Then open: http://localhost:8501"
