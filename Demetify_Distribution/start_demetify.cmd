@echo off
echo.
echo ========================================
echo    🧠 DEMETIFY - Starting Application
echo ========================================
echo.
echo Checking Python installation...

REM Check if python is available
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Python found
    goto :start_app
)

py --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Python found (py launcher)
    set PYTHON_CMD=py
    goto :start_app
)

python3 --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Python found (python3)
    set PYTHON_CMD=python3
    goto :start_app
)

echo ❌ Python not found. Please install Python 3.8+ from python.org
echo.
pause
exit /b 1

:start_app
if not defined PYTHON_CMD set PYTHON_CMD=python

echo Installing dependencies (this may take a moment)...
%PYTHON_CMD% -m pip install --user -r requirements.txt >nul 2>&1

echo.
echo 🚀 Starting Demetify...
echo ⏳ Please wait for the web interface to load...
echo 🌐 Your browser will open automatically
echo 📍 Manual URL: http://localhost:8501
echo.
echo Press Ctrl+C to stop the application
echo ========================================
echo.

%PYTHON_CMD% -m streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address 0.0.0.0