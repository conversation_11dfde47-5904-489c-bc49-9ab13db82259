🧠 DEMETIFY - Q<PERSON>CK START GUIDE
================================

🚀 FASTEST WAY TO RUN:
1. Open Command Prompt (cmd) in this folder
2. Type: python start.py
3. Press Enter
4. <PERSON><PERSON><PERSON> will open automatically!

📍 Manual URL: http://localhost:8501

🔧 ALTERNATIVE METHODS:
- python -m streamlit run demetify_ncomms2022_app.py
- start_demetify.cmd (double-click)

📖 DETAILED INSTRUCTIONS:
- See UNIVERSITY_INSTRUCTIONS.md for troubleshooting
- See README.md for complete documentation

👥 TEAM:
Advisors: Dr. <PERSON><PERSON>, Dr. <PERSON><PERSON><PERSON>, Prof. <PERSON><PERSON>,
          Prof<PERSON>, Prof. <PERSON><PERSON>, Prof. <PERSON><PERSON>
Team: <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>
University of Illinois at Urbana-Champaign

🎯 WHAT IT DOES:
- Upload MRI scans (.nii or .nii.gz files)
- Get AI-powered Alzheimer's disease assessment
- View SHAP interpretability analysis
- Professional radiologist-friendly interface