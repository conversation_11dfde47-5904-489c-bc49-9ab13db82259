#!/usr/bin/env python3
"""
Simple Demetify Starter - University PC Friendly
Just run: python start.py
"""

import subprocess
import sys
import os
import webbrowser
import time

def print_header():
    print("\n" + "="*50)
    print("    🧠 DEMETIFY - Alzheimer's Assessment")
    print("="*50)
    print("\nAdvisors: Dr. <PERSON>, Dr. <PERSON>,")
    print("          Prof. <PERSON><PERSON><PERSON><PERSON>, Prof. <PERSON><PERSON>,")
    print("          Prof. <PERSON>, Prof. <PERSON><PERSON>")
    print("\nTeam: <PERSON><PERSON><PERSON>, <PERSON>,")
    print("      <PERSON><PERSON>, <PERSON><PERSON><PERSON>")
    print("\nUniversity of Illinois at Urbana-Champaign")
    print("="*50)

def check_requirements():
    """Install requirements if needed"""
    try:
        print("\n📦 Checking dependencies...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", "--user", "-r", "requirements.txt"],
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Dependencies ready")
        else:
            print("⚠️  Some dependencies may need manual installation")
    except Exception as e:
        print(f"⚠️  Dependency check failed: {e}")

def start_streamlit():
    """Start the Streamlit application"""
    try:
        print("\n🚀 Starting Demetify...")
        print("⏳ Please wait for the web interface to load...")
        print("🌐 Browser will open automatically")
        print("📍 Manual URL: http://localhost:8501")
        print("\n💡 Press Ctrl+C to stop the application")
        print("-" * 50)

        # Start Streamlit
        process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", "demetify_ncomms2022_app.py",
            "--server.port", "8501",
            "--server.address", "0.0.0.0",
            "--server.headless", "true"
        ])

        # Wait a moment then try to open browser
        time.sleep(3)
        try:
            webbrowser.open("http://localhost:8501")
        except:
            pass  # Browser opening is optional

        # Wait for the process
        process.wait()

    except KeyboardInterrupt:
        print("\n\n🛑 Shutting down Demetify...")
        if 'process' in locals():
            process.terminate()
    except Exception as e:
        print(f"\n❌ Error starting Demetify: {e}")
        print("\nTry running manually:")
        print("python -m streamlit run demetify_ncomms2022_app.py")

def main():
    print_header()

    # Check if we're in the right directory
    if not os.path.exists("demetify_ncomms2022_app.py"):
        print("\n❌ Error: demetify_ncomms2022_app.py not found!")
        print("Please run this script from the Demetify directory.")
        input("\nPress Enter to exit...")
        return

    check_requirements()
    start_streamlit()

if __name__ == "__main__":
    main()