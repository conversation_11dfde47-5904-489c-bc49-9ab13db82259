#!/usr/bin/env python3
"""
Build script for creating a portable Demetify executable
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import platform

def install_pyinstaller():
    """Install PyInstaller if not available"""
    try:
        import PyInstaller
        print("✓ PyInstaller already installed")
        return True
    except ImportError:
        print("Installing PyInstaller...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
            print("✓ PyInstaller installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"✗ Failed to install PyInstaller: {e}")
            return False

def create_icon():
    """Create a simple icon file if it doesn't exist"""
    icon_path = Path("demetify.ico")
    if not icon_path.exists():
        print("Creating default icon...")
        # For now, we'll skip icon creation as it requires additional dependencies
        # The spec file will handle the case where no icon exists
        pass

def build_executable():
    """Build the executable using PyInstaller"""
    try:
        print("Building portable executable...")

        # Clean previous builds
        if Path("dist").exists():
            shutil.rmtree("dist")
        if Path("build").exists():
            shutil.rmtree("build")

        # Run PyInstaller
        cmd = [sys.executable, "-m", "PyInstaller", "demetify.spec", "--clean"]
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)

        print("✓ Executable built successfully")
        return True

    except subprocess.CalledProcessError as e:
        print(f"✗ Build failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def create_portable_package():
    """Create a portable package with all necessary files"""
    try:
        print("Creating portable package...")

        # Create portable directory
        portable_dir = Path("Demetify_Portable")
        if portable_dir.exists():
            shutil.rmtree(portable_dir)
        portable_dir.mkdir()

        # Copy executable
        exe_name = "Demetify.exe" if platform.system() == "Windows" else "Demetify"
        exe_path = Path("dist") / exe_name

        if exe_path.exists():
            shutil.copy2(exe_path, portable_dir / exe_name)
            print(f"✓ Copied {exe_name}")
        else:
            print(f"✗ Executable not found: {exe_path}")
            return False

        # Copy essential files
        essential_files = [
            "README.md",
            "requirements.txt"
        ]

        for file in essential_files:
            if Path(file).exists():
                shutil.copy2(file, portable_dir / file)
                print(f"✓ Copied {file}")

        # Copy model directory
        model_dir = Path("ncomms2022_original")
        if model_dir.exists():
            shutil.copytree(model_dir, portable_dir / "ncomms2022_original")
            print("✓ Copied model files")

        print(f"✓ Portable package created in: {portable_dir}")
        return True

    except Exception as e:
        print(f"✗ Failed to create portable package: {e}")
        return False

def main():
    """Main build process"""
    print("🧠 Demetify Portable Builder")
    print("=" * 40)

    # Check if we're in the right directory
    if not Path("demetify_launcher.py").exists():
        print("✗ Please run this script from the Demetify directory")
        return False

    # Install PyInstaller
    if not install_pyinstaller():
        return False

    # Create icon
    create_icon()

    # Build executable
    if not build_executable():
        return False

    # Create portable package
    if not create_portable_package():
        return False

    print("\n🎉 Build completed successfully!")
    print("\nTo distribute:")
    print("1. Copy the 'Demetify_Portable' folder to any Windows PC")
    print("2. Double-click 'Demetify.exe' to run")
    print("3. No installation required!")

    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)