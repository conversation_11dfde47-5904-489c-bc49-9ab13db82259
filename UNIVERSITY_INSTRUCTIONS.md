# 🧠 Demetify - University PC Instructions

**For Windows PCs with restricted permissions (Windows Defender blocking executables)**

---

## 🎯 **Quick Start for University PCs**

### **Method 1: Simple Python Command (Recommended)**
1. **Open Command Prompt** (cmd)
2. **Navigate to Demetify folder**:
   ```
   cd path\to\demetify\folder
   ```
3. **Run the starter**:
   ```
   python start.py
   ```
4. **Wait for browser to open** automatically at http://localhost:8501

### **Method 2: Direct Streamlit Command**
1. **Open Command Prompt** (cmd)
2. **Navigate to folder**:
   ```
   cd path\to\demetify\folder
   ```
3. **Install dependencies** (first time only):
   ```
   python -m pip install --user -r requirements.txt
   ```
4. **Start application**:
   ```
   python -m streamlit run demetify_ncomms2022_app.py
   ```

### **Method 3: Batch File (if allowed)**
1. **Double-click** `start_demetify.cmd`
2. **Or run from cmd**:
   ```
   start_demetify.cmd
   ```

---

## 📋 **Requirements**

- **Python 3.8+** (usually pre-installed on university PCs)
- **Internet connection** (for first-time dependency installation)
- **Web browser** (Chrome, Firefox, Edge)

---

## 🔧 **Troubleshooting**

### **"Python not found"**
Try these commands:
```
py start.py
python3 start.py
```

### **"Permission denied" for pip install**
Use user installation:
```
python -m pip install --user streamlit torch numpy pandas matplotlib scikit-learn nibabel nilearn shap
```

### **Port 8501 already in use**
Change port:
```
python -m streamlit run demetify_ncomms2022_app.py --server.port 8502
```
Then go to: http://localhost:8502

### **Browser doesn't open automatically**
Manually open browser and go to: **http://localhost:8501**

---

## 🚀 **What Happens When You Run It**

1. ✅ **Dependencies install** automatically (first time)
2. ✅ **Web server starts** on your local machine
3. ✅ **Browser opens** to the application
4. ✅ **Upload MRI scans** (.nii or .nii.gz files)
5. ✅ **Get AI analysis** with interpretability

---

## 📞 **Support**

**If nothing works, try the manual approach:**

1. **Install Python packages**:
   ```
   python -m pip install --user streamlit torch numpy pandas matplotlib scikit-learn scipy nibabel nilearn shap tqdm pillow
   ```

2. **Run Streamlit directly**:
   ```
   python -m streamlit run demetify_ncomms2022_app.py --server.port 8501
   ```

3. **Open browser manually**: http://localhost:8501

---

## 👥 **Credits**

**Advisors:** Dr. Aimee Yu-Ballard, Dr. Suguna Pappu, Prof. Ujjal Kumar Mukherjee, Prof. Sridhar Seshadri, Prof. Vijaya Kolachalama, Prof. Boby George

**Team:** Vatsal Mitesh Tailor, Avery Bryson Buehler, Laya P Krishnan, Humza Ahmed

*University of Illinois at Urbana-Champaign*