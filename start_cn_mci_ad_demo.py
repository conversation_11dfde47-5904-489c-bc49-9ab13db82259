#!/usr/bin/env python3
"""
Startup script for CN/MCI/AD 3-Category Classification Demo
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """Install required packages"""
    print("🔧 Installing requirements...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements_cn_mci_ad.txt"
        ])
        print("✅ Requirements installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False
    return True

def check_model_file():
    """Check if model file exists"""
    model_path = Path("memory_efficient_cnn_model.pth")
    if model_path.exists():
        print(f"✅ Model file found: {model_path}")
        print(f"   Size: {model_path.stat().st_size / 1024 / 1024:.1f} MB")
        return True
    else:
        print("⚠️  Model file not found: memory_efficient_cnn_model.pth")
        print("   Demo will run in simulation mode")
        return False

def start_streamlit():
    """Start the Streamlit application"""
    print("🚀 Starting CN/MCI/AD Classification Demo...")
    print("   Opening in browser at: http://localhost:8501")
    print("   Press Ctrl+C to stop")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "cn_mci_ad_frontend.py",
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
    except KeyboardInterrupt:
        print("\n👋 Demo stopped by user")
    except Exception as e:
        print(f"❌ Failed to start demo: {e}")

def main():
    print("🧠 Demetify - CN/MCI/AD 3-Category Classification Demo")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("cn_mci_ad_frontend.py").exists():
        print("❌ Frontend file not found. Please run from the correct directory.")
        return
    
    # Install requirements
    if not install_requirements():
        return
    
    # Check model file
    check_model_file()
    
    print("\n" + "=" * 60)
    print("🎯 Demo Features:")
    print("   • 3-category classification: CN, MCI, AD")
    print("   • Real-time MRI analysis")
    print("   • Probability visualization")
    print("   • Professional radiologist interface")
    print("=" * 60)
    
    # Start the demo
    start_streamlit()

if __name__ == "__main__":
    main()
