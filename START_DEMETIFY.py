#!/usr/bin/env python3
"""
Demetify - One-Click Start (Python Script)
Bypasses Windows Defender issues with batch files
"""

import os
import sys
import subprocess
import platform
import time
import urllib.request
import tempfile
import shutil

def print_banner():
    """Print the Demetify banner"""
    print("\n" + "="*70)
    print("  ██████╗ ███████╗███╗   ███╗███████╗████████╗██╗███████╗██╗   ██╗")
    print("  ██╔══██╗██╔════╝████╗ ████║██╔════╝╚══██╔══╝██║██╔════╝╚██╗ ██╔╝")
    print("  ██║  ██║█████╗  ██╔████╔██║█████╗     ██║   ██║█████╗   ╚████╔╝ ")
    print("  ██║  ██║██╔══╝  ██║╚██╔╝██║██╔══╝     ██║   ██║██╔══╝    ╚██╔╝  ")
    print("  ██████╔╝███████╗██║ ╚═╝ ██║███████╗   ██║   ██║██║        ██║   ")
    print("  ╚═════╝ ╚══════╝╚═╝     ╚═╝╚══════╝   ╚═╝   ╚═╝╚═╝        ╚═╝   ")
    print("\n  AI-Powered Alzheimer's Disease Assessment System")
    print("  University of Illinois at Urbana-Champaign")
    print("  Project Lead: Prof. <PERSON><PERSON> Se<PERSON>dri")
    print("="*70)

def run_command(cmd, shell=True, capture_output=False):
    """Run a command safely"""
    try:
        if capture_output:
            result = subprocess.run(cmd, shell=shell, capture_output=True, text=True)
            return result.returncode == 0, result.stdout, result.stderr
        else:
            result = subprocess.run(cmd, shell=shell)
            return result.returncode == 0, "", ""
    except Exception as e:
        return False, "", str(e)

def check_conda():
    """Check if conda is available"""
    success, stdout, stderr = run_command("conda --version", capture_output=True)
    return success

def check_environment():
    """Check if demetify environment exists and works"""
    if platform.system() == "Windows":
        # Try to activate and test
        cmd = 'conda activate demetify && python -c "import torch; print(\'OK\')"'
        success, stdout, stderr = run_command(cmd, capture_output=True)
        return success and "OK" in stdout
    else:
        # For Linux/Mac
        cmd = 'source ~/miniconda3/etc/profile.d/conda.sh && conda activate demetify && python -c "import torch; print(\'OK\')"'
        success, stdout, stderr = run_command(cmd, capture_output=True)
        return success and "OK" in stdout

def install_miniconda():
    """Install Miniconda"""
    print("[INFO] Installing Miniconda (Python environment)...")
    
    system = platform.system()
    if system == "Windows":
        url = "https://repo.anaconda.com/miniconda/Miniconda3-latest-Windows-x86_64.exe"
        installer_name = "Miniconda3-latest-Windows-x86_64.exe"
    elif system == "Darwin":  # macOS
        url = "https://repo.anaconda.com/miniconda/Miniconda3-latest-MacOSX-x86_64.sh"
        installer_name = "Miniconda3-latest-MacOSX-x86_64.sh"
    else:  # Linux
        url = "https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh"
        installer_name = "Miniconda3-latest-Linux-x86_64.sh"
    
    # Download installer
    temp_dir = tempfile.mkdtemp()
    installer_path = os.path.join(temp_dir, installer_name)
    
    print("Downloading Miniconda installer...")
    try:
        urllib.request.urlretrieve(url, installer_path)
    except Exception as e:
        print(f"[ERROR] Failed to download installer: {e}")
        return False
    
    # Install
    print("Installing Miniconda...")
    if system == "Windows":
        cmd = f'"{installer_path}" /InstallationType=JustMe /RegisterPython=1 /S /D=%USERPROFILE%\\miniconda3'
        success, _, _ = run_command(cmd)
        time.sleep(30)  # Wait for installation
    else:
        home_dir = os.path.expanduser("~")
        miniconda_dir = os.path.join(home_dir, "miniconda3")
        cmd = f'bash "{installer_path}" -b -p "{miniconda_dir}"'
        success, _, _ = run_command(cmd)
    
    # Clean up
    shutil.rmtree(temp_dir, ignore_errors=True)
    
    return success

def setup_environment():
    """Set up the demetify environment"""
    print("[INFO] Setting up Demetify environment...")
    
    # Create environment
    print("Creating Python environment...")
    success, _, _ = run_command("conda create -n demetify python=3.9 -y")
    if not success:
        print("[WARNING] Environment creation had issues, but continuing...")
    
    # Install PyTorch
    print("Installing PyTorch (this may take several minutes)...")
    if platform.system() == "Windows":
        cmd = 'conda activate demetify && conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y'
        success, _, _ = run_command(cmd)
        if not success:
            print("Falling back to CPU version...")
            cmd = 'conda activate demetify && conda install pytorch torchvision torchaudio cpuonly -c pytorch -y'
            run_command(cmd)
    else:
        cmd = 'source ~/miniconda3/etc/profile.d/conda.sh && conda activate demetify && conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y'
        success, _, _ = run_command(cmd)
        if not success:
            print("Falling back to CPU version...")
            cmd = 'source ~/miniconda3/etc/profile.d/conda.sh && conda activate demetify && conda install pytorch torchvision torchaudio cpuonly -c pytorch -y'
            run_command(cmd)
    
    # Install other dependencies
    print("Installing other dependencies...")
    if platform.system() == "Windows":
        cmd = 'conda activate demetify && pip install -r requirements.txt'
    else:
        cmd = 'source ~/miniconda3/etc/profile.d/conda.sh && conda activate demetify && pip install -r requirements.txt'
    
    success, _, _ = run_command(cmd)
    if not success:
        print("[WARNING] Some dependencies failed to install, but system should still work")
    
    return True

def create_launcher():
    """Create launcher script"""
    print("[INFO] Creating launcher...")
    
    if platform.system() == "Windows":
        launcher_content = '''@echo off
title Demetify - Running
echo 🧠 Starting Demetify...
echo.
conda activate demetify
echo ✅ Environment activated
echo 🚀 Launching Demetify frontend...
echo    Access at: http://localhost:8501
echo    Press Ctrl+C to stop
echo.
streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address 0.0.0.0
pause
'''
        with open("launch_demetify.bat", "w") as f:
            f.write(launcher_content)
    else:
        launcher_content = '''#!/bin/bash
echo "🧠 Starting Demetify..."
echo ""
source ~/miniconda3/etc/profile.d/conda.sh
conda activate demetify
echo "✅ Environment activated"
echo "🚀 Launching Demetify frontend..."
echo "   Access at: http://localhost:8501"
echo "   Press Ctrl+C to stop"
echo ""
streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address 0.0.0.0
'''
        with open("launch_demetify.sh", "w") as f:
            f.write(launcher_content)
        os.chmod("launch_demetify.sh", 0o755)

def test_installation():
    """Test the installation"""
    print("[INFO] Testing installation...")
    
    if platform.system() == "Windows":
        cmd = 'conda activate demetify && python test_system.py'
    else:
        cmd = 'source ~/miniconda3/etc/profile.d/conda.sh && conda activate demetify && python test_system.py'
    
    success, stdout, stderr = run_command(cmd, capture_output=True)
    
    if success and "All tests passed" in stdout:
        print("[SUCCESS] ✅ Installation completed successfully!")
        return True
    else:
        print("[WARNING] Installation completed with some issues, but should still work")
        return True

def launch_demetify():
    """Launch the Demetify application"""
    print("\n" + "="*70)
    print("🚀 LAUNCHING DEMETIFY")
    print("="*70)
    print("\nThe system will start in a few seconds...")
    print("Once started, open your web browser to: http://localhost:8501")
    print("\nTo stop the system: Press Ctrl+C in the terminal window")
    print("")
    
    # Launch
    if platform.system() == "Windows":
        if os.path.exists("launch_demetify.bat"):
            os.system("launch_demetify.bat")
        else:
            # Direct launch
            os.system('conda activate demetify && streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address 0.0.0.0')
    else:
        if os.path.exists("launch_demetify.sh"):
            os.system("./launch_demetify.sh")
        else:
            # Direct launch
            os.system('source ~/miniconda3/etc/profile.d/conda.sh && conda activate demetify && streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address 0.0.0.0')

def main():
    """Main function"""
    print_banner()
    print()
    
    # Check if already installed and working
    if check_environment():
        print("[SUCCESS] Demetify is already installed and ready!")
        print()
        launch_demetify()
        return
    
    print("[INFO] First-time setup required. Installing Demetify...")
    print("This will take 10-15 minutes depending on your internet connection.")
    print()
    
    try:
        input("Press Enter to continue...")
    except:
        pass  # Handle cases where input() might not work
    
    # Install Miniconda if needed
    if not check_conda():
        if not install_miniconda():
            print("[ERROR] Failed to install Miniconda")
            input("Press Enter to exit...")
            return
    else:
        print("[SUCCESS] Conda already available")
    
    # Set up environment
    setup_environment()
    
    # Create launcher
    create_launcher()
    
    # Test installation
    test_installation()
    
    # Launch
    launch_demetify()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n[INFO] Installation cancelled by user")
    except Exception as e:
        print(f"\n[ERROR] An error occurred: {e}")
        input("Press Enter to exit...")
