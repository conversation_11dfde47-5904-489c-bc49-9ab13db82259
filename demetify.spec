# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from pathlib import Path

# Get the current directory
current_dir = Path.cwd()

# Define data files to include
data_files = [
    ('demetify_ncomms2022_app.py', '.'),
    ('ncomms2022_model_enhanced.py', '.'),
    ('ncomms2022_preprocessing_fsl.py', '.'),
    ('ncomms2022_shap.py', '.'),
    ('requirements.txt', '.'),
    ('test_system.py', '.'),
    ('ncomms2022_original', 'ncomms2022_original'),
]

# Add all files from ncomms2022_original directory
ncomms_dir = current_dir / 'ncomms2022_original'
if ncomms_dir.exists():
    for root, dirs, files in os.walk(ncomms_dir):
        for file in files:
            src_path = Path(root) / file
            rel_path = src_path.relative_to(current_dir)
            dest_dir = str(rel_path.parent)
            data_files.append((str(src_path), dest_dir))

block_cipher = None

a = Analysis(
    ['demetify_launcher.py'],
    pathex=[],
    binaries=[],
    datas=data_files,
    hiddenimports=[
        'streamlit',
        'torch',
        'torchvision',
        'numpy',
        'pandas',
        'matplotlib',
        'scikit-learn',
        'scipy',
        'nibabel',
        'nilearn',
        'shap',
        'tqdm',
        'PIL',
        'tkinter',
        'threading',
        'webbrowser',
        'subprocess',
        'pathlib',
        'tempfile',
        'logging',
        'json',
        'time',
        'platform',
        'urllib',
        'zipfile',
        'shutil'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Demetify',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='demetify.ico' if Path('demetify.ico').exists() else None,
)