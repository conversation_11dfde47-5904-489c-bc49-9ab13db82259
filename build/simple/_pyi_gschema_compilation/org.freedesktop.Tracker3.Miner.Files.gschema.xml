<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2011, Nokia <<EMAIL>>

This library is free software; you can redistribute it and/or
modify it under the terms of the GNU Lesser General Public
License as published by the Free Software Foundation; either
version 2.1 of the License, or (at your option) any later version.

This library is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANT<PERSON>ILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
Lesser General Public License for more details.

You should have received a copy of the GNU Lesser General Public
License along with this library; if not, write to the
Free Software Foundation, Inc., 51 Franklin Street, Fifth Floor,
Boston, MA  02110-1301, USA.
-->
<schemalist>
  <schema id="org.freedesktop.Tracker3.Miner.Files" path="/org/freedesktop/tracker/miner/files/" gettext-domain="tracker-miners">
    <key name="initial-sleep" type="i">
      <summary>Initial sleep</summary>
      <description>Initial sleep time, in seconds.</description>
      <range min="0" max="1000"/>
      <default>15</default>
    </key>

    <key name="throttle" type="i">
      <summary>Throttle</summary>
      <description>Indexing speed, the higher the slower.</description>
      <range min="0" max="20"/>
      <default>0</default>
    </key>

    <key name="low-disk-space-limit" type="i">
      <summary>Low disk space limit</summary>
      <description>Disk space threshold in percent at which to pause indexing, or -1 to disable.</description>
      <range min="-1" max="100"/>
      <default>-1</default>
    </key>

    <key name="crawling-interval" type="i">
      <summary>Crawling interval</summary>
      <description>
        Interval in days to check whether the filesystem is up to date in the database.
	0 forces crawling anytime, -1 forces it only after unclean shutdowns, and -2
	disables it entirely.
      </description>
      <range min="-2" max="365"/>
      <default>-1</default>
    </key>

    <key name="removable-days-threshold" type="i">
      <summary>Removable devices’ data permanence threshold</summary>
      <description>
	Threshold in days after which files from removables devices
	will be removed from database if not mounted. 0 means never,
	maximum is 365.
      </description>
      <range min="0" max="365"/>
      <default>3</default>
    </key>

    <key name="enable-monitors" type="b">
      <summary>Enable monitors</summary>
      <description>Set to false to completely disable any file monitoring</description>
      <default>true</default>
    </key>

    <key name="index-removable-devices" type="b">
      <summary>Index removable devices</summary>
      <description>Set to true to enable indexing mounted directories for removable devices.</description>
      <default>false</default>
    </key>

    <key name="index-optical-discs" type="b">
      <summary>Index optical discs</summary>
      <description>
	Set to true to enable indexing CDs, DVDs, and generally optical media
	(if removable devices are not indexed, optical discs won’t be either)
      </description>
      <default>false</default>
    </key>

    <key name="index-on-battery" type="b">
      <summary>Index when running on battery</summary>
      <description>Set to true to index while running on battery</description>
      <default>true</default>
    </key>

    <key name="index-on-battery-first-time" type="b">
      <summary>Perform initial indexing when running on battery</summary>
      <description>Set to true to index while running on battery for the first time only</description>
      <default>true</default>
    </key>

    <key name="index-recursive-directories" type="as">
      <summary>Directories to index recursively</summary>
      <!-- Translators: Do NOT translate the directories names in capital. Those
      are keys used by Tracker. -->
      <description>
	List of directories to index recursively, Special values include:
	‘&amp;DESKTOP’, ‘&amp;DOCUMENTS’, ‘&amp;DOWNLOAD’, ‘&amp;MUSIC’, ‘&amp;PICTURES’,
	‘&amp;PUBLIC_SHARE’, ‘&amp;TEMPLATES’, ‘&amp;VIDEOS’.

	See /etc/xdg/user-dirs.defaults and $HOME/.config/user-dirs.default
      </description>
      <default>[ '&amp;DESKTOP', '&amp;DOCUMENTS', '&amp;MUSIC', '&amp;PICTURES', '&amp;VIDEOS' ]</default>
    </key>

    <key name="index-single-directories" type="as">
      <summary>Directories to index non-recursively</summary>
      <!-- Translators: Do NOT translate the directories names in capital. Those
      are keys used by Tracker. -->
      <description>
	List of directories to index without inspecting subfolders, Special values include:
	‘&amp;DESKTOP’, ‘&amp;DOCUMENTS’, ‘&amp;DOWNLOAD’, ‘&amp;MUSIC’, ‘&amp;PICTURES’,
	‘&amp;PUBLIC_SHARE’, ‘&amp;TEMPLATES’, ‘&amp;VIDEOS’.

	See /etc/xdg/user-dirs.defaults and $HOME/.config/user-dirs.default
      </description>
      <default>[ '$HOME', '&amp;DOWNLOAD' ]</default>
    </key>

    <key name="index-applications" type="b">
      <summary>Index applications installed on the system</summary>
      <description>
          If this option is enabled, the filesystem miner will look for .desktop
          files in the standard XDG system and user data directories.
      </description>
      <default>true</default>
    </key>

    <key name="ignored-files" type="as">
      <summary>Ignored files</summary>
      <description>List of file patterns to avoid</description>
      <default>[ '*~', '*.o', '*.la', '*.lo' , '*.loT', '*.in', '*.csproj', '*.m4', '*.rej', '*.gmo', '*.orig', '*.pc', '*.omf', '*.aux', '*.tmp', '*.vmdk', '*.vm*', '*.nvram', '*.part', '*.rcore', '*.lzo', 'autom4te', 'conftest', 'confstat', 'Makefile', 'SCCS', 'ltmain.sh', 'libtool', 'config.status', 'confdefs.h', 'configure', '#*#', '~$*.doc?', '~$*.dot?', '~$*.xls?', '~$*.xlt?', '~$*.xlam', '~$*.ppt?', '~$*.pot?', '~$*.ppam', '~$*.ppsm', '~$*.ppsx', '~$*.vsd?', '~$*.vss?', '~$*.vst?', 'mimeapps.list', 'mimeinfo.cache', 'gnome-mimeapps.list', 'kde-mimeapps.list', '*.directory' ]</default>
    </key>

    <key name="ignored-directories" type="as">
      <summary>Ignored directories</summary>
      <description>List of directories to avoid</description>
      <default>[ 'po', 'CVS', 'core-dumps', 'lost+found' ]</default>
    </key>

    <key name="ignored-directories-with-content" type="as">
      <summary>Ignored directories with content</summary>
      <description>Avoid any directory containing a file blocklisted here</description>
      <default>[ '.trackerignore', '.git', '.hg', '.nomedia' ]</default>
    </key>
  </schema>
</schemalist>
