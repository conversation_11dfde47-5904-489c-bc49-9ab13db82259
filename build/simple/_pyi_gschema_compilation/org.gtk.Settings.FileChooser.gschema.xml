<?xml version="1.0" encoding="UTF-8"?>
<!--
  Copyright © 2010 Christian <PERSON>

  This library is free software; you can redistribute it and/or modify
  it under the terms of the GNU Lesser General Public License as published by
  the Free Software Foundation; either version 2.1, or (at your option)
  any later version.

  This library is distributed in the hope that it will be useful,
  but WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  GNU Lesser General Public License for more details.

  You should have received a copy of the GNU Lesser General Public License
  along with this library. If not, see <http://www.gnu.org/licenses/>.
-->
<schemalist>

  <enum id='org.gtk.Settings.FileChooser.LocationMode'>
    <value nick='path-bar' value='0'/>
    <value nick='filename-entry' value='1'/>
  </enum>

  <enum id='org.gtk.Settings.FileChooser.SortColumn'>
    <value nick='name' value='0'/>
    <value nick='size' value='1'/>
    <value nick='type' value='2'/>
    <value nick='modified' value='3'/>
  </enum>

  <enum id='org.gtk.Settings.FileChooser.SortOrder'>
    <value nick='ascending' value='0'/>
    <value nick='descending' value='1'/>
  </enum>

  <enum id='org.gtk.Settings.FileChooser.StartupMode'>
    <value nick='recent' value='0'/>
    <value nick='cwd' value='1'/>
  </enum>

  <enum id='org.gtk.Settings.FileChooser.ClockFormat'>
    <value nick='24h' value='0'/>
    <value nick='12h' value='1'/>
  </enum>

  <enum id='org.gtk.Settings.FileChooser.DateFormat'>
    <value nick='regular' value='0'/>
    <value nick='with-time' value='1'/>
  </enum>

  <enum id='org.gtk.Settings.FileChooser.TypeFormat'>
    <value nick='mime' value='0'/>
    <value nick='description' value='1'/>
    <value nick='category' value='2'/>
  </enum>

  <schema id='org.gtk.Settings.FileChooser' path='/org/gtk/settings/file-chooser/'>
    <key name='last-folder-uri' type='s'>
      <default>""</default>
    </key>
    <key name='location-mode' enum='org.gtk.Settings.FileChooser.LocationMode'>
      <default>'path-bar'</default>
      <summary>Location mode</summary>
      <description>
	Controls whether the file chooser shows just a path bar, or a visible entry
        for the filename as well, for the benefit of typing-oriented users. The
        possible values for these modes are "path-bar" and "filename-entry".
      </description>
    </key>
    <key name='show-hidden' type='b'>
      <default>false</default>
      <summary>Show hidden files</summary>
      <description>
	Controls whether the file chooser shows hidden files or not.
      </description>
    </key>
    <key type="b" name="sort-directories-first">
      <default>false</default>
      <summary>Show folders first</summary>
      <description>
        If set to true, then folders are shown before files in the list.
      </description>
    </key>
    <key name='expand-folders' type='b'>
      <default>false</default>
      <summary>Expand folders</summary>
      <description>This key is deprecated; do not use it.</description>
    </key>
    <key name='show-size-column' type='b'>
      <default>true</default>
      <summary>Show file sizes</summary>
      <description>
	Controls whether the file chooser shows a column with file sizes.
      </description>
    </key>
    <key name='show-type-column' type='b'>
      <default>true</default>
      <summary>Show file types</summary>
      <description>
	Controls whether the file chooser shows a column with file types.
      </description>
    </key>
    <key name='sort-column' enum='org.gtk.Settings.FileChooser.SortColumn'>
      <default>'name'</default>
      <summary>Sort column</summary>
      <description>
	Can be one of "name", "modified", or "size".  It controls
	which of the columns in the file chooser is used for sorting
	the list of files.
      </description>
    </key>
    <key name='sort-order' enum='org.gtk.Settings.FileChooser.SortOrder'>
      <default>'ascending'</default>
      <summary>Sort order</summary>
      <description>
	Can be one of the strings "ascending" or "descending".
      </description>
    </key>
    <key name='window-position' type='(ii)'>
      <default>(-1, -1)</default>
      <summary>Window position</summary>
      <description>
	The (x, y) coordinates of the upper-left corner of the GtkFileChooserDialog's
        window.
      </description>
    </key>
    <key name='window-size' type='(ii)'>
      <default>(-1, -1)</default>
      <summary>Window size</summary>
      <description>
	The size (width, height) of the GtkFileChooserDialog's window, in pixels.
      </description>
    </key>
    <key name='startup-mode' enum='org.gtk.Settings.FileChooser.StartupMode'>
      <default>'recent'</default>
      <summary>Startup mode</summary>
      <description>
	Either "recent" or "cwd"; controls whether the file chooser
	starts up showing the list of recently-used files, or the
	contents of the current working directory.
      </description>
    </key>
    <key name='sidebar-width' type='i'>
      <default>148</default>
      <summary>Sidebar width</summary>
      <description>
	Width in pixels of the file chooser's places sidebar.
      </description>
    </key>
    <key name="clock-format" enum="org.gtk.Settings.FileChooser.ClockFormat">
      <default>'24h'</default>
      <summary>Time format</summary>
      <description>
        Whether the time is shown in 24h or 12h format.
      </description>
    </key>
    <key name="date-format" enum="org.gtk.Settings.FileChooser.DateFormat">
      <default>'regular'</default>
      <summary>Date format</summary>
      <description>
        The amount of detail to show in the Modified column.
      </description>
    </key>
    <key name="type-format" enum="org.gtk.Settings.FileChooser.TypeFormat">
      <default>'category'</default>
      <summary>Type format</summary>
      <description>
        Different ways to show the 'Type' column information.
        Example outputs for a video mp4 file:
        'mime' -> 'video/mp4'
        'description' -> 'MPEG-4 video'
        'category' -> 'Video'
      </description>
    </key>
  </schema>

</schemalist>
