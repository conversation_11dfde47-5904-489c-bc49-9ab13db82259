
<!-- This file is generated by glib-mkenums, do not modify it. This code is licensed under the same license as the containing project. Note that it links to GLib, so must comply with the LGPL linking clauses. -->

<schemalist>
<enum id="org.freedesktop.TrackerMiners3.TrackerSerializationFormat">
    <value nick="sparql" value="0"/>
    <value nick="turtle" value="1"/>
    <value nick="json" value="2"/>
    <value nick="json-ld" value="3"/>
  </enum>
<enum id="org.freedesktop.TrackerMiners3.TrackerIndexLocationFlags">
    <value nick="none" value="0"/>
  </enum>
</schemalist>

<!-- Generated data ends here -->

