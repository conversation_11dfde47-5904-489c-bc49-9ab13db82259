
<!-- This file is generated by glib-mkenums, do not modify it. This code is licensed under the same license as the containing project. Note that it links to GLib, so must comply with the LGPL linking clauses. -->

<schemalist>
  <enum id="org.gnome.gedit.GeditNotebookShowTabsModeType">
    <value nick="never" value="0"/>
    <value nick="auto" value="1"/>
    <value nick="always" value="2"/>
  </enum>
</schemalist>

<!-- Generated data ends here -->

