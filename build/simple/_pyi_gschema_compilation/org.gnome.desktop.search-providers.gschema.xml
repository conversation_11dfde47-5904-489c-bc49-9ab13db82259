<?xml version="1.0" encoding="UTF-8"?>
<schemalist gettext-domain="gsettings-desktop-schemas">
  <schema id="org.gnome.desktop.search-providers" path="/org/gnome/desktop/search-providers/">

    <key name="disable-external" type="b">
      <default>false</default>
      <summary>Disable all external search providers</summary>
      <description>
        Set to true to disable all external search provider programs,
        whether or not they are independently disabled or enabled.
        External search providers are installed by applications in
        $XDG_DATA_DIR/gnome-shell/search-providers.
      </description>
    </key>

    <key name="disabled" type="as">
      <default>[]</default>
      <summary>
        List of desktop file IDs for which the associated default-enabled search provider
        should be disabled
      </summary>
      <description>
        Results for applications contained in this list will not be displayed
        when searching.
      </description>
    </key>

    <key name="enabled" type="as">
      <default>[]</default>
      <summary>
        List of desktop file IDs for which the associated default-disabled search provider
        should be enabled
      </summary>
      <description>
        Results for applications contained in this list will be displayed
        when searching.
      </description>
    </key>

    <key name="sort-order" type="as">
      <default>['org.gnome.Contacts.desktop', 'org.gnome.Documents.desktop', 'org.gnome.Nautilus.desktop']</default>
      <summary>List of desktop file IDs for search providers sort order</summary>
      <description>
        Results for applications contained in this list will be displayed in
        the specified order.
        Results for applications not specified in this list will be displayed
        last, sorted alphabetically.
      </description>
    </key>

  </schema>
</schemalist>
