<?xml version="1.0" encoding="UTF-8"?>
<schemalist gettext-domain="gsettings-desktop-schemas">
  <schema id="org.gnome.desktop.a11y.applications" path="/org/gnome/desktop/a11y/applications/">
    <key name="screen-keyboard-enabled" type="b">
      <default>false</default>
      <summary>On-screen keyboard</summary>
      <description>Whether the on-screen keyboard is turned on.</description>
    </key>
    <key name="screen-magnifier-enabled" type="b">
      <default>false</default>
      <summary>Screen magnifier</summary>
      <description>Whether the screen magnifier is turned on.</description>
    </key>
    <key name="screen-reader-enabled" type="b">
      <default>false</default>
      <summary>Screen reader</summary>
      <description>Whether the screen reader is turned on.</description>
    </key>
  </schema>
</schemalist>
