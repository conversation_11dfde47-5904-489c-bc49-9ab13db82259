
<!-- This file is generated by glib-mkenums, do not modify it. This code is licensed under the same license as the containing project. Note that it links to GLib, so must comply with the LGPL linking clauses. -->

<schemalist>
  <enum id="org.gnome.gedit.plugins.filebrowser.GeditFileBrowserError">
    <value nick="none" value="0"/>
    <value nick="rename" value="1"/>
    <value nick="delete" value="2"/>
    <value nick="new-file" value="3"/>
    <value nick="new-directory" value="4"/>
    <value nick="open-directory" value="5"/>
    <value nick="set-root" value="6"/>
    <value nick="load-directory" value="7"/>
    <value nick="num" value="8"/>
  </enum>
  <enum id="org.gnome.gedit.plugins.filebrowser.GeditFileBrowserStoreColumn">
    <value nick="icon" value="0"/>
    <value nick="icon-name" value="1"/>
    <value nick="markup" value="2"/>
    <value nick="location" value="3"/>
    <value nick="flags" value="4"/>
    <value nick="name" value="5"/>
    <value nick="emblem" value="6"/>
    <value nick="num" value="7"/>
  </enum>
  <flags id="org.gnome.gedit.plugins.filebrowser.GeditFileBrowserStoreFlag">
    <value nick="is-directory" value="1"/>
    <value nick="is-hidden" value="2"/>
    <value nick="is-text" value="4"/>
    <value nick="loaded" value="8"/>
    <value nick="is-filtered" value="16"/>
    <value nick="is-dummy" value="32"/>
  </flags>
  <enum id="org.gnome.gedit.plugins.filebrowser.GeditFileBrowserStoreResult">
    <value nick="ok" value="0"/>
    <value nick="no-change" value="1"/>
    <value nick="error" value="2"/>
    <value nick="no-trash" value="3"/>
    <value nick="mounting" value="4"/>
    <value nick="num" value="5"/>
  </enum>
  <flags id="org.gnome.gedit.plugins.filebrowser.GeditFileBrowserStoreFilterMode">
    <value nick="none" value="0"/>
    <value nick="hide-hidden" value="1"/>
    <value nick="hide-binary" value="2"/>
  </flags>
  <enum id="org.gnome.gedit.plugins.filebrowser.GeditFileBrowserViewClickPolicy">
    <value nick="single" value="0"/>
    <value nick="double" value="1"/>
  </enum>
</schemalist>

<!-- Generated data ends here -->

