<?xml version="1.0" encoding="UTF-8"?>
<schemalist gettext-domain="gsettings-desktop-schemas">
  <schema id="org.gnome.desktop.session" path="/org/gnome/desktop/session/">
    <key name="idle-delay" type="u">
      <default>300</default>
      <summary>Time before session is considered idle</summary>
      <description>The number of seconds of inactivity before the session is considered idle.</description>
    </key>
    <key name="session-name" type="s">
      <default>"gnome"</default>
      <summary>Session type</summary>
      <description>The name of the session to use.  Known value is
      “gnome”.</description>
    </key>
  </schema>
</schemalist>
