<?xml version="1.0" encoding="UTF-8"?>
<schemalist gettext-domain="gsettings-desktop-schemas">
  <schema id="org.gnome.desktop.default-applications" path="/org/gnome/desktop/applications/">
    <child name="office" schema="org.gnome.desktop.default-applications.office"/>
    <child name="terminal" schema="org.gnome.desktop.default-applications.terminal"/>
  </schema>
  <schema id="org.gnome.desktop.default-applications.office" path="/org/gnome/desktop/applications/office/">
    <child name="calendar" schema="org.gnome.desktop.default-applications.office.calendar"/>
    <child name="tasks" schema="org.gnome.desktop.default-applications.office.tasks"/>
  </schema>
  <schema id="org.gnome.desktop.default-applications.office.calendar" path="/org/gnome/desktop/applications/office/calendar/">
    <key name="exec" type="s">
      <default>'evolution -c calendar'</default>
      <summary>Default calendar</summary>
      <description>
        Default calendar application

        DEPRECATED: This key is deprecated and ignored.
        The preferred calendar application is the application handling the text/calendar mime type.
      </description>
    </key>
    <key name="needs-term" type="b">
      <default>false</default>
      <summary>Calendar needs terminal</summary>
      <description>
        Whether the default calendar application needs a terminal to run.

        DEPRECATED: This key is deprecated and ignored.
        The preferred calendar application is the application handling the text/calendar mime type,
        whether it needs a terminal is taken from its desktop file.
      </description>
    </key>
  </schema>
  <schema id="org.gnome.desktop.default-applications.office.tasks" path="/org/gnome/desktop/applications/office/tasks/">
    <key name="exec" type="s">
      <default>'evolution -c tasks'</default>
      <summary>Default tasks</summary>
      <description>
        Default tasks application.
      </description>
    </key>
    <key name="needs-term" type="b">
      <default>false</default>
      <summary>Tasks needs terminal</summary>
      <description>
        Whether the default tasks application needs a terminal to run.
      </description>
    </key>
  </schema>
  <schema id="org.gnome.desktop.default-applications.terminal" path="/org/gnome/desktop/applications/terminal/">
    <key name="exec" type="s">
      <default>'gnome-terminal'</default>
      <summary>Terminal application</summary>
      <description>
        Terminal program to use when starting applications that require one.

        DEPRECATED: This key is deprecated and ignored.
        The default terminal is handled in GIO.
      </description>
    </key>
    <key name="exec-arg" type="s">
      <default>'-x'</default>
      <summary>Exec Arguments</summary>
      <description>
        Argument used to execute programs in the terminal defined by the 
        “exec” key.

        DEPRECATED: This key is deprecated and ignored.
        The default terminal and how to invoke it is handled in GIO.
      </description>
    </key>
  </schema>
</schemalist>
