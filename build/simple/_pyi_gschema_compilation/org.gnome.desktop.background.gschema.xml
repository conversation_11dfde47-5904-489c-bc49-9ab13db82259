<?xml version="1.0" encoding="UTF-8"?>
<schemalist gettext-domain="gsettings-desktop-schemas">
  <schema id="org.gnome.desktop.background" path="/org/gnome/desktop/background/">
    <key name="picture-options" enum="org.gnome.desktop.GDesktopBackgroundStyle">
      <default>'zoom'</default>
      <summary>Picture Options</summary>
      <description>
        Determines how the image set by wallpaper_filename is rendered.
        Possible values are “none”, “wallpaper”, “centered”, “scaled”,
        “stretched”, “zoom”, “spanned”.
      </description>
    </key>
    <key name="picture-uri" type="s">
      <default>'file:///usr/share/backgrounds/gnome/adwaita-l.jpg'</default>
      <summary>Picture URI</summary>
      <description>
        URI to use for the background image. Note that the backend only supports
        local (file://) URIs.
      </description>
    </key>
    <key name="picture-uri-dark" type="s">
      <default>'file:///usr/share/backgrounds/gnome/adwaita-d.jpg'</default>
      <summary>Picture URI (dark)</summary>
      <description>
        URI to use for the background image. Note that the backend only supports
        local (file://) URIs.
      </description>
    </key>
    <key name="picture-opacity" type="i">
      <range min="0" max="100"/>
      <default>100</default>
      <summary>Picture Opacity</summary>
      <description>
        Opacity with which to draw the background picture.
      </description>
    </key>
    <key name="primary-color" type="s">
      <default>'#023c88'</default>
      <summary>Primary Color</summary>
      <description>
        Left or Top color when drawing gradients, or the solid color.
      </description>
    </key>
    <key name="secondary-color" type="s">
      <default>'#5789ca'</default>
      <summary>Secondary Color</summary>
      <description>
        Right or Bottom color when drawing gradients, not used for solid color.
      </description>
    </key>
    <key name="color-shading-type" enum="org.gnome.desktop.GDesktopBackgroundShading">
      <default>'solid'</default>
      <summary>Color Shading Type</summary>
      <description>
        How to shade the background color. Possible values are “horizontal”,
        “vertical”, and “solid”.
      </description>
    </key>
    <key name="show-desktop-icons" type="b">
      <default>false</default>
      <summary>Have file manager handle the desktop</summary>
      <description>
        If set to true, then file manager will draw the icons on the desktop.
      </description>
    </key>
  </schema>
</schemalist>
