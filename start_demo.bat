@echo off
echo ========================================
echo  Demetify - CN/MCI/AD Classification
echo  3-Category Dementia Assessment Demo
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo Python found: 
python --version

REM Check if pip is available
python -m pip --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: pip is not available
    echo Please ensure pip is installed with Python
    pause
    exit /b 1
)

echo.
echo Installing requirements...
python -m pip install -r requirements_cn_mci_ad.txt

echo.
echo Checking model file...
if exist "memory_efficient_cnn_model.pth" (
    echo Model file found: memory_efficient_cnn_model.pth
) else (
    echo WARNING: Model file not found
    echo Demo will run in simulation mode
)

echo.
echo ========================================
echo  Starting CN/MCI/AD Classification Demo
echo ========================================
echo.
echo Demo will open in your browser at:
echo http://localhost:8501
echo.
echo Press Ctrl+C to stop the demo
echo.

REM Start the Streamlit app
python -m streamlit run cn_mci_ad_frontend.py --server.port 8501 --browser.gatherUsageStats false

echo.
echo Demo stopped.
pause
