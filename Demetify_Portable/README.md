# 🧠 Demetify - Alzheimer's Disease Assessment System

**Professional AI-powered Alzheimer's Disease Assessment using Structural MRI Scans**

*University of Illinois at Urbana-Champaign | Project Lead: Prof<PERSON> <PERSON><PERSON>shadri*

---

## 🚀 **Quick Start - Click & Run**

### **Option 1: Portable Executable (Recommended for Windows)**
1. **Build**: Double-click `BUILD_PORTABLE.bat` (one-time setup)
2. **Run**: Copy `Demetify_Portable` folder to any PC and double-click `Demetify.exe`
3. **No installation needed** - works on any Windows PC!

### **Option 2: Python Launcher (Windows Defender Friendly)**
1. **Run**: Double-click `RUN_DEMETIFY.py` or `demetify_launcher.py`
2. **Auto-setup**: Dependencies install automatically
3. **Access**: <PERSON>rowser opens automatically to http://localhost:8501

### **Option 3: Traditional Method**
- **Windows**: Double-click `INSTALL_DEMETIFY.bat` then `launch_demetify.bat`
- **Linux/Mac**: Run `./INSTALL_DEMETIFY.sh` then `./launch_demetify.sh`

---

## 📋 **System Requirements**

- **OS**: Windows 10+ (portable version), or any OS with Python 3.8+
- **RAM**: 8GB minimum (16GB recommended)
- **Storage**: 5GB free space
- **Internet**: Required for initial setup only

---

## 🎯 **Features**

- ✅ **MRI Preprocessing** - Automatic T1 scan processing
- ✅ **AD/CN Classification** - Real-time deep learning predictions
- ✅ **SHAP Interpretability** - Visual explanations in seconds
- ✅ **Professional Interface** - Radiologist-friendly orientations
- ✅ **Portable Deployment** - Copy & run on any Windows PC

---

## 📊 **Usage**

1. **Upload** T1-weighted MRI scan (.nii or .nii.gz)
2. **View** original and preprocessed scans
3. **Get** AD/CN classification with confidence scores
4. **Examine** SHAP interpretability heatmaps
5. **Review** risk assessment and cognitive scores

---

## 🔧 **Troubleshooting**

**Windows Defender Issues:**
- Use `RUN_DEMETIFY.py` instead of .bat files
- Or build portable version with `BUILD_PORTABLE.bat`

**Runtime Issues:**
- Ensure all files stay in same directory
- Check port 8501 is available
- Dependencies install automatically

---

## 📞 **Support**

- **Model**: Nature Communications 2022 publication
- **Performance**: 85%+ accuracy validated against neurologists
- **Processing**: Complete analysis in <2 minutes per scan

*Demetify v2.0 - Portable & Windows Defender Friendly*
