"""
Enhanced AD/CN Classification Model for ncomms2022
Loads pre-trained weights and performs inference on preprocessed MRI scans
"""

import torch
import torch.nn as nn
import numpy as np
import os
from pathlib import Path
import logging
from typing import Union, Tuple, Dict, Optional
import json

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Model architecture definitions (fallback if import fails)
class ConvLayer(nn.Module):
    def __init__(self, in_channels, out_channels, kernel, pooling, drop_rate, BN=True, relu_type='leaky'):
        super(ConvLayer, self).__init__()
        kernel_size, kernel_stride, kernel_padding = kernel
        pool_kernel, pool_stride, pool_padding = pooling
        self.conv = nn.Conv3d(in_channels, out_channels, kernel_size, kernel_stride, kernel_padding, bias=False)
        self.pooling = nn.MaxPool3d(pool_kernel, pool_stride, pool_padding)
        self.BN = nn.BatchNorm3d(out_channels)
        self.relu = nn.LeakyReLU() if relu_type=='leaky' else nn.ReLU()
        self.dropout = nn.Dropout(drop_rate) 
       
    def forward(self, x):
        x = self.conv(x)
        x = self.pooling(x)
        x = self.BN(x)
        x = self.relu(x)
        x = self.dropout(x)
        return x

class _CNN_Bone(nn.Module):
    def __init__(self, config):
        super(_CNN_Bone, self).__init__()
        num, p = config['fil_num'], config['drop_rate']
        self.block1 = ConvLayer(1, num, (7, 2, 0), (3, 2, 0), p)
        self.block2 = ConvLayer(num, 2*num, (4, 1, 0), (2, 2, 0), p)
        self.block3 = ConvLayer(2*num, 4*num, (3, 1, 0), (2, 2, 0), p)
        self.block4 = ConvLayer(4*num, 8*num, (3, 1, 0), (2, 2, 0), p)
        self.size = self.test_size()

    def forward(self, x):
        x = self.block1(x)
        x = self.block2(x)
        x = self.block3(x)
        x = self.block4(x)
        batch_size = x.shape[0]
        x = x.view(batch_size, -1)
        return x

    def test_size(self):
        case = torch.ones((1, 1, 182, 218, 182))
        output = self.forward(case)
        return output.shape[1]

class MLP(nn.Module):
    def __init__(self, in_size, config):
        super(MLP, self).__init__()
        fil_num, drop_rate, out_size = config['fil_num'], config['drop_rate'], config['out_size']
        self.dense1 = nn.Sequential(
            nn.Dropout(drop_rate),
            nn.Linear(in_size, fil_num),
        )
        self.dense2 = nn.Sequential(
            nn.LeakyReLU(),
            nn.Dropout(drop_rate),
            nn.Linear(fil_num, out_size),
        )

    def forward(self, x, get_intermediate_score=False):
        x = self.dense1(x)
        if get_intermediate_score:
            return x
        x = self.dense2(x)
        return x

class Model(nn.Module):
    def __init__(self, backbone, mlp, task):
        super(Model, self).__init__()
        self.backbone = backbone
        self.mlp = mlp
        self.softmax = nn.Softmax(dim=1) if task == 'ADD' else None

    def forward(self, x):
        x = self.backbone(x)
        x = self.mlp(x)
        if self.softmax: 
            x = self.softmax(x)
        return x

class NCOMMSClassifier:
    """
    AD/CN Classification using pre-trained ncomms2022 model
    """
    
    def __init__(self, 
                 model_dir: str = "ncomms2022_original/checkpoint_dir/CNN_baseline_new_cross0",
                 config_path: str = "ncomms2022_original/task_config.json",
                 device: Optional[str] = None):
        """
        Initialize the classifier
        
        Args:
            model_dir: Directory containing pre-trained model weights
            config_path: Path to task configuration file
            device: Device to use ('cuda', 'cpu', or None for auto-detect)
        """
        self.model_dir = Path(model_dir)
        self.config_path = Path(config_path)
        
        # Set device
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        logger.info(f"Using device: {self.device}")
        
        # Load configuration
        self.config = self._load_config()
        
        # Initialize models
        self.add_model = None
        self.cog_model = None
        
        # Load models
        self._load_models()
    
    def _load_config(self) -> Dict:
        """Load task configuration"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r') as f:
                    config = json.load(f)
                return config
            else:
                # Default configuration if file not found
                logger.warning(f"Config file not found: {self.config_path}. Using default config.")
                return {
                    "backbone": {"fil_num": 20, "drop_rate": 0},
                    "ADD": {"fil_num": 30, "drop_rate": 0.5, "out_size": 2},
                    "COG": {"fil_num": 100, "drop_rate": 0.5, "out_size": 1}
                }
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return {
                "backbone": {"fil_num": 20, "drop_rate": 0},
                "ADD": {"fil_num": 30, "drop_rate": 0.5, "out_size": 2},
                "COG": {"fil_num": 100, "drop_rate": 0.5, "out_size": 1}
            }
    
    def _load_models(self):
        """Load pre-trained models"""
        try:
            # Initialize shared backbone
            self.backbone = _CNN_Bone(self.config['backbone']).to(self.device)
            backbone_size = self.backbone.size

            # Load backbone weights
            backbone_weights_path = self.model_dir / "backbone_58.pth"
            if backbone_weights_path.exists():
                backbone_state_dict = torch.load(backbone_weights_path, map_location=self.device)
                self.backbone.load_state_dict(backbone_state_dict)
                logger.info(f"Loaded backbone weights from {backbone_weights_path}")
            else:
                logger.warning(f"Backbone weights not found: {backbone_weights_path}")

            # Initialize ADD MLP
            self.add_mlp = MLP(backbone_size, self.config['ADD']).to(self.device)

            # Load ADD MLP weights
            add_weights_path = self.model_dir / "ADD_58.pth"
            if add_weights_path.exists():
                add_state_dict = torch.load(add_weights_path, map_location=self.device)
                self.add_mlp.load_state_dict(add_state_dict)
                logger.info(f"Loaded ADD MLP weights from {add_weights_path}")
            else:
                logger.warning(f"ADD weights not found: {add_weights_path}")

            # Initialize COG MLP
            self.cog_mlp = MLP(backbone_size, self.config['COG']).to(self.device)

            # Load COG MLP weights
            cog_weights_path = self.model_dir / "COG_58.pth"
            if cog_weights_path.exists():
                cog_state_dict = torch.load(cog_weights_path, map_location=self.device)
                self.cog_mlp.load_state_dict(cog_state_dict)
                logger.info(f"Loaded COG MLP weights from {cog_weights_path}")
            else:
                logger.warning(f"COG weights not found: {cog_weights_path}")

            # Set models to evaluation mode
            self.backbone.eval()
            self.add_mlp.eval()
            self.cog_mlp.eval()

            # Create combined models for compatibility
            self.add_model = Model(self.backbone, self.add_mlp, 'ADD')
            self.cog_model = Model(self.backbone, self.cog_mlp, 'COG')

        except Exception as e:
            logger.error(f"Error loading models: {e}")
            raise
    
    def predict_add_probability(self, mri_data: np.ndarray) -> Tuple[float, float]:
        """
        Predict AD probability from MRI data

        Args:
            mri_data: Preprocessed MRI data (182, 218, 182)

        Returns:
            Tuple of (CN_probability, AD_probability)
        """
        if self.backbone is None or self.add_mlp is None:
            raise ValueError("ADD model components not loaded")

        try:
            # Prepare input tensor
            if len(mri_data.shape) == 3:
                # Add batch and channel dimensions
                input_tensor = torch.from_numpy(mri_data).float().unsqueeze(0).unsqueeze(0)
            elif len(mri_data.shape) == 4:
                # Add batch dimension
                input_tensor = torch.from_numpy(mri_data).float().unsqueeze(0)
            else:
                input_tensor = torch.from_numpy(mri_data).float()

            input_tensor = input_tensor.to(self.device)

            # Perform inference
            with torch.no_grad():
                # Extract features with backbone
                features = self.backbone(input_tensor)

                # Get ADD predictions
                add_output = self.add_mlp(features)

                # Apply softmax for probabilities
                probabilities = torch.softmax(add_output, dim=1)

                # Extract probabilities
                cn_prob = probabilities[0, 0].item()  # Normal cognition
                ad_prob = probabilities[0, 1].item()  # Alzheimer's disease

                return cn_prob, ad_prob

        except Exception as e:
            logger.error(f"Error during ADD prediction: {e}")
            raise
    
    def predict_cog_score(self, mri_data: np.ndarray) -> float:
        """
        Predict cognitive score from MRI data

        Args:
            mri_data: Preprocessed MRI data (182, 218, 182)

        Returns:
            Predicted cognitive score
        """
        if self.backbone is None or self.cog_mlp is None:
            raise ValueError("COG model components not loaded")

        try:
            # Prepare input tensor
            if len(mri_data.shape) == 3:
                # Add batch and channel dimensions
                input_tensor = torch.from_numpy(mri_data).float().unsqueeze(0).unsqueeze(0)
            elif len(mri_data.shape) == 4:
                # Add batch dimension
                input_tensor = torch.from_numpy(mri_data).float().unsqueeze(0)
            else:
                input_tensor = torch.from_numpy(mri_data).float()

            input_tensor = input_tensor.to(self.device)

            # Perform inference
            with torch.no_grad():
                # Extract features with backbone
                features = self.backbone(input_tensor)

                # Get COG predictions
                cog_output = self.cog_mlp(features)
                cog_score = cog_output[0, 0].item()

                return cog_score

        except Exception as e:
            logger.error(f"Error during COG prediction: {e}")
            raise
    
    def predict_full(self, mri_data: np.ndarray) -> Dict:
        """
        Perform full prediction (ADD probability + COG score)
        
        Args:
            mri_data: Preprocessed MRI data (182, 218, 182)
            
        Returns:
            Dictionary with prediction results
        """
        results = {}
        
        try:
            # ADD prediction
            cn_prob, ad_prob = self.predict_add_probability(mri_data)
            results['cn_probability'] = cn_prob
            results['ad_probability'] = ad_prob
            results['predicted_class'] = 'AD' if ad_prob > cn_prob else 'CN'
            results['confidence'] = max(cn_prob, ad_prob)
            
            # COG prediction
            cog_score = self.predict_cog_score(mri_data)
            results['cog_score'] = cog_score
            
            # Risk assessment
            if ad_prob > 0.7:
                results['risk_level'] = 'High'
            elif ad_prob > 0.4:
                results['risk_level'] = 'Moderate'
            else:
                results['risk_level'] = 'Low'
            
            return results
            
        except Exception as e:
            logger.error(f"Error during full prediction: {e}")
            raise


def load_classifier(model_dir: str = None, device: str = None) -> NCOMMSClassifier:
    """
    Convenience function to load the classifier
    
    Args:
        model_dir: Directory containing pre-trained weights
        device: Device to use
        
    Returns:
        Initialized classifier
    """
    if model_dir is None:
        model_dir = "ncomms2022_original/checkpoint_dir/CNN_baseline_new_cross0"
    
    return NCOMMSClassifier(model_dir=model_dir, device=device)
