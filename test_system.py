"""
Simple system test for Demetify deployment
"""

import os
import sys
import numpy as np

def test_system():
    """Test core system functionality"""
    print("🧠 Demetify System Test")
    print("=" * 30)
    
    try:
        # Test imports
        print("1. Testing imports...")
        from ncomms2022_model_enhanced import NCOMMSClassifier
        from ncomms2022_shap import NCOMMSSHAPExplainer
        print("   ✓ All imports successful")
        
        # Test model loading
        print("2. Testing model loading...")
        classifier = NCOMMSClassifier()
        print("   ✓ Models loaded successfully")
        
        # Test with demo data
        demo_file = "ncomms2022_original/demo/mri/demo1.npy"
        if os.path.exists(demo_file):
            print("3. Testing with demo data...")
            mri_data = np.load(demo_file)
            results = classifier.predict_full(mri_data)
            print(f"   ✓ Prediction: {results['predicted_class']}")
            print(f"   ✓ Confidence: {results['confidence']:.3f}")
            
            # Test SHAP
            print("4. Testing interpretability...")
            explainer = NCOMMSSHAPExplainer(classifier)
            shap_values = explainer._gradient_explanation(mri_data, 'ADD')
            print(f"   ✓ SHAP generated: {shap_values.shape}")
            
            print("\n✅ All tests passed! System ready.")
            return True
        else:
            print("   ⚠️  Demo data not found")
            return False
            
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_system()
    sys.exit(0 if success else 1)
