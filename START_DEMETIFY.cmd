@echo off
REM Simple CMD launcher that bypasses Windows Defender
REM This runs the Python script instead of complex batch operations

title Demetify Launcher

echo Starting Demetify...
echo.

REM Try to run with Python
python START_DEMETIFY.py
if %ERRORLEVEL% EQU 0 goto end

REM If Python not found, try py launcher
py START_DEMETIFY.py
if %ERRORLEVEL% EQU 0 goto end

REM If still no Python, try to install it first
echo Python not found. Attempting to install...
echo.
echo Please install Python from: https://www.python.org/downloads/
echo Then run this script again.
echo.
pause

:end
echo.
echo Demetify session ended.
pause
